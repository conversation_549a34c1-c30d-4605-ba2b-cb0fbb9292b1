using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;

namespace StudentManagementSystem.Models.ViewModels
{
    public class StudentViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Full name is required")]
        [StringLength(100, ErrorMessage = "Full name cannot exceed 100 characters")]
        [Display(Name = "Full Name")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string Email { get; set; } = string.Empty;

        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        [Display(Name = "Phone Number")]
        public string? Phone { get; set; }

        [Required(ErrorMessage = "Date of birth is required")]
        [DataType(DataType.Date)]
        [Display(Name = "Date of Birth")]
        public DateTime DateOfBirth { get; set; }

        [Required(ErrorMessage = "Gender is required")]
        public Gender Gender { get; set; }

        [Required(ErrorMessage = "Department is required")]
        [Display(Name = "Department")]
        public int DepartmentId { get; set; }

        [Required(ErrorMessage = "Enrollment date is required")]
        [DataType(DataType.Date)]
        [Display(Name = "Enrollment Date")]
        public DateTime EnrollmentDate { get; set; } = DateTime.Now;

        [Display(Name = "Profile Photo")]
        public IFormFile? ProfilePhoto { get; set; }

        public string? ProfilePhotoPath { get; set; }

        [Display(Name = "Student ID")]
        [StringLength(20, ErrorMessage = "Student ID cannot exceed 20 characters")]
        public string? StudentId { get; set; }

        public bool IsActive { get; set; } = true;

        // For dropdown
        public IEnumerable<SelectListItem>? Departments { get; set; }
    }

    public class StudentListViewModel
    {
        public IEnumerable<Student> Students { get; set; } = new List<Student>();
        public string? SearchTerm { get; set; }
        public int? DepartmentFilter { get; set; }
        public int? EnrollmentYearFilter { get; set; }
        public IEnumerable<SelectListItem>? Departments { get; set; }
        public IEnumerable<SelectListItem>? EnrollmentYears { get; set; }
        public int TotalStudents { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int TotalPages { get; set; }
    }

    public class DashboardViewModel
    {
        public int TotalStudents { get; set; }
        public int TotalDepartments { get; set; }
        public int ActiveStudents { get; set; }
        public int NewStudentsThisMonth { get; set; }
        public IEnumerable<DepartmentStatistic> DepartmentStatistics { get; set; } = new List<DepartmentStatistic>();
        public IEnumerable<EnrollmentStatistic> EnrollmentStatistics { get; set; } = new List<EnrollmentStatistic>();
    }

    public class DepartmentStatistic
    {
        public string DepartmentName { get; set; } = string.Empty;
        public int StudentCount { get; set; }
        public string Color { get; set; } = string.Empty;
    }

    public class EnrollmentStatistic
    {
        public int Year { get; set; }
        public int Count { get; set; }
    }
}
