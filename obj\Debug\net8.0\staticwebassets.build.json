{"Version": 1, "Hash": "3mECa0aB35czcTwU+y3ZozjiaeMbJooFjtZWTyNgmCI=", "Source": "StudentManagementSystem", "BasePath": "_content/StudentManagementSystem", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "StudentManagementSystem\\wwwroot", "Source": "StudentManagementSystem", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\TaskApp\\wwwroot\\", "BasePath": "_content/StudentManagementSystem", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Desktop\\TaskApp\\wwwroot\\css\\site.css", "SourceId": "StudentManagementSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\TaskApp\\wwwroot\\", "BasePath": "_content/StudentManagementSystem", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qibwuup2ps", "Integrity": "UigFIF298E0wW4Yxf+0SxBWIFiPhGIqcTlCVj65BrmQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 6924, "LastWriteTime": "2025-07-03T04:45:50+00:00"}], "Endpoints": [{"Route": "css/site.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\TaskApp\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6924"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UigFIF298E0wW4Yxf+0SxBWIFiPhGIqcTlCVj65BrmQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 04:45:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UigFIF298E0wW4Yxf+0SxBWIFiPhGIqcTlCVj65BrmQ="}]}, {"Route": "css/site.qibwuup2ps.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\TaskApp\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6924"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UigFIF298E0wW4Yxf+0SxBWIFiPhGIqcTlCVj65BrmQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 04:45:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qibwuup2ps"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-UigFIF298E0wW4Yxf+0SxBWIFiPhGIqcTlCVj65BrmQ="}]}]}