@model IEnumerable<StudentManagementSystem.Models.Department>
@{
    ViewData["Title"] = "Departments";
}

<!-- Header Section -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">
            <i class="bi bi-building-fill text-primary me-2"></i>
            Departments Management
        </h2>
        <p class="text-muted mb-0">Manage academic departments</p>
    </div>
    <a href="/Departments/Create" class="btn btn-gradient-primary">
        <i class="bi bi-building-add me-2"></i>
        Add New Department
    </a>
</div>

<!-- Departments Grid -->
<div class="row g-4">
    @foreach (var department in Model)
    {
        <div class="col-lg-6 col-xl-4">
            <div class="card border-0 shadow-sm h-100 department-card">
                <div class="card-body p-4">
                    <!-- Department Header -->
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle bg-gradient-primary d-flex align-items-center justify-content-center text-white fw-bold" 
                                 style="width: 60px; height: 60px; font-size: 1.5rem;">
                                @department.Code
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="card-title mb-1 fw-bold">@department.Name</h5>
                            <p class="text-muted small mb-0">Code: @department.Code</p>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge @(department.IsActive ? "bg-success" : "bg-secondary")">
                                @(department.IsActive ? "Active" : "Inactive")
                            </span>
                        </div>
                    </div>

                    <!-- Department Details -->
                    <div class="department-details mb-3">
                        @if (!string.IsNullOrEmpty(department.Description))
                        {
                            <p class="text-muted mb-2">@department.Description</p>
                        }
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-people-fill text-primary me-2"></i>
                            <small class="text-muted">@department.Students.Count students enrolled</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="bi bi-calendar-fill text-success me-2"></i>
                            <small class="text-muted">Created: @department.CreatedDate.ToString("MMM yyyy")</small>
                        </div>
                    </div>

                    <!-- Progress Bar for Student Count -->
                    @{
                        var maxStudents = Model.Any() ? Model.Max(d => d.Students.Count) : 1;
                        var percentage = maxStudents > 0 ? (department.Students.Count * 100.0 / maxStudents) : 0;
                    }
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small class="text-muted">Student Enrollment</small>
                            <small class="text-muted">@department.Students.Count</small>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-gradient-primary" style="width: @percentage%"></div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex gap-2">
                        <a href="/Departments/Details/@department.Id" class="btn btn-outline-primary btn-sm flex-fill">
                            <i class="bi bi-eye-fill me-1"></i>
                            View
                        </a>
                        <a href="/Departments/Edit/@department.Id" class="btn btn-outline-success btn-sm flex-fill">
                            <i class="bi bi-pencil-fill me-1"></i>
                            Edit
                        </a>
                        @if (department.Students.Count == 0)
                        {
                            <a href="/Departments/Delete/@department.Id" class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-trash-fill"></i>
                            </a>
                        }
                        else
                        {
                            <button class="btn btn-outline-secondary btn-sm" disabled title="Cannot delete department with students">
                                <i class="bi bi-lock-fill"></i>
                            </button>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@if (!Model.Any())
{
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
        </div>
        <h4 class="text-muted">No Departments Found</h4>
        <p class="text-muted">Create your first department to get started.</p>
        <a href="/Departments/Create" class="btn btn-gradient-primary">
            <i class="bi bi-building-add me-2"></i>
            Add First Department
        </a>
    </div>
}

<!-- Statistics Summary -->
<div class="row g-4 mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-info text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    Department Statistics
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded-3">
                            <div class="fs-3 fw-bold text-primary">@Model.Count()</div>
                            <div class="text-muted">Total Departments</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded-3">
                            <div class="fs-3 fw-bold text-success">@Model.Count(d => d.IsActive)</div>
                            <div class="text-muted">Active Departments</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded-3">
                            <div class="fs-3 fw-bold text-warning">@Model.Sum(d => d.Students.Count)</div>
                            <div class="text-muted">Total Students</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded-3">
                            <div class="fs-3 fw-bold text-info">@(Model.Any() ? Math.Round(Model.Average(d => d.Students.Count), 1) : 0)</div>
                            <div class="text-muted">Avg Students/Dept</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .department-card {
        transition: all 0.3s ease;
        border-radius: 1rem !important;
    }

    .department-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
    }

    .department-details {
        border-left: 3px solid var(--primary-color);
        padding-left: 1rem;
        margin-left: 0.5rem;
    }

    .progress-bar.bg-gradient-primary {
        background: linear-gradient(90deg, #6366F1, #4F46E5) !important;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #6366F1, #4F46E5) !important;
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #06B6D4, #0891B2) !important;
    }
</style>
