// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file is used by ../analyze_snippet_code_test.dart, which depends on the
// precise contents (including especially the comments) of this file.

// Examples can assume:
// int x = ''; // error (invalid_assignment)

/// ```dart
/// print(x);
/// ```
String? bar;

/// error: empty dart block
/// ```dart
/// ```
String? foo;
