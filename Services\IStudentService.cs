using StudentManagementSystem.Models;
using StudentManagementSystem.Models.ViewModels;

namespace StudentManagementSystem.Services
{
    public interface IStudentService
    {
        Task<IEnumerable<Student>> GetAllStudentsAsync();
        Task<Student?> GetStudentByIdAsync(int id);
        Task<Student> CreateStudentAsync(Student student);
        Task<Student> UpdateStudentAsync(Student student);
        Task<bool> DeleteStudentAsync(int id);
        Task<StudentListViewModel> GetFilteredStudentsAsync(string? searchTerm, int? departmentId, int? enrollmentYear, int page, int pageSize);
        Task<bool> StudentExistsAsync(int id);
        Task<bool> EmailExistsAsync(string email, int? excludeId = null);
        Task<bool> StudentIdExistsAsync(string studentId, int? excludeId = null);
        Task<IEnumerable<int>> GetEnrollmentYearsAsync();
        Task<int> GetTotalStudentsAsync();
        Task<int> GetActiveStudentsAsync();
        Task<int> GetNewStudentsThisMonthAsync();
        Task<IEnumerable<DepartmentStatistic>> GetDepartmentStatisticsAsync();
        Task<IEnumerable<EnrollmentStatistic>> GetEnrollmentStatisticsAsync();
    }
}
