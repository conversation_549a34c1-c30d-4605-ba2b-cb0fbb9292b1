using Microsoft.EntityFrameworkCore;
using StudentManagementSystem.Data;
using StudentManagementSystem.Models;
using StudentManagementSystem.Models.ViewModels;

namespace StudentManagementSystem.Services
{
    public class StudentService : IStudentService
    {
        private readonly ApplicationDbContext _context;

        public StudentService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Student>> GetAllStudentsAsync()
        {
            return await _context.Students
                .Include(s => s.Department)
                .OrderByDescending(s => s.CreatedDate)
                .ToListAsync();
        }

        public async Task<Student?> GetStudentByIdAsync(int id)
        {
            return await _context.Students
                .Include(s => s.Department)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<Student> CreateStudentAsync(Student student)
        {
            student.CreatedDate = DateTime.Now;
            student.UpdatedDate = DateTime.Now;
            
            _context.Students.Add(student);
            await _context.SaveChangesAsync();
            return student;
        }

        public async Task<Student> UpdateStudentAsync(Student student)
        {
            student.UpdatedDate = DateTime.Now;
            
            _context.Students.Update(student);
            await _context.SaveChangesAsync();
            return student;
        }

        public async Task<bool> DeleteStudentAsync(int id)
        {
            var student = await _context.Students.FindAsync(id);
            if (student == null) return false;

            _context.Students.Remove(student);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<StudentListViewModel> GetFilteredStudentsAsync(string? searchTerm, int? departmentId, int? enrollmentYear, int page, int pageSize)
        {
            var query = _context.Students.Include(s => s.Department).AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(s => s.FullName.Contains(searchTerm) || s.Email.Contains(searchTerm));
            }

            if (departmentId.HasValue)
            {
                query = query.Where(s => s.DepartmentId == departmentId.Value);
            }

            if (enrollmentYear.HasValue)
            {
                query = query.Where(s => s.EnrollmentDate.Year == enrollmentYear.Value);
            }

            var totalStudents = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalStudents / (double)pageSize);

            var students = await query
                .OrderByDescending(s => s.CreatedDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new StudentListViewModel
            {
                Students = students,
                SearchTerm = searchTerm,
                DepartmentFilter = departmentId,
                EnrollmentYearFilter = enrollmentYear,
                TotalStudents = totalStudents,
                CurrentPage = page,
                PageSize = pageSize,
                TotalPages = totalPages
            };
        }

        public async Task<bool> StudentExistsAsync(int id)
        {
            return await _context.Students.AnyAsync(s => s.Id == id);
        }

        public async Task<bool> EmailExistsAsync(string email, int? excludeId = null)
        {
            var query = _context.Students.Where(s => s.Email == email);
            if (excludeId.HasValue)
            {
                query = query.Where(s => s.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        public async Task<bool> StudentIdExistsAsync(string studentId, int? excludeId = null)
        {
            var query = _context.Students.Where(s => s.StudentId == studentId);
            if (excludeId.HasValue)
            {
                query = query.Where(s => s.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        public async Task<IEnumerable<int>> GetEnrollmentYearsAsync()
        {
            return await _context.Students
                .Select(s => s.EnrollmentDate.Year)
                .Distinct()
                .OrderByDescending(y => y)
                .ToListAsync();
        }

        public async Task<int> GetTotalStudentsAsync()
        {
            return await _context.Students.CountAsync();
        }

        public async Task<int> GetActiveStudentsAsync()
        {
            return await _context.Students.CountAsync(s => s.IsActive);
        }

        public async Task<int> GetNewStudentsThisMonthAsync()
        {
            var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            return await _context.Students.CountAsync(s => s.CreatedDate >= startOfMonth);
        }

        public async Task<IEnumerable<DepartmentStatistic>> GetDepartmentStatisticsAsync()
        {
            // Beautiful, compatible color palette
            var colors = new[] {
                "#6366F1", // Indigo
                "#EC4899", // Pink
                "#10B981", // Emerald
                "#F59E0B", // Amber
                "#8B5CF6", // Violet
                "#06B6D4", // Cyan
                "#EF4444", // Red
                "#84CC16"  // Lime
            };

            var stats = await _context.Students
                .Include(s => s.Department)
                .GroupBy(s => s.Department!.Name)
                .Select(g => new DepartmentStatistic
                {
                    DepartmentName = g.Key,
                    StudentCount = g.Count()
                })
                .ToListAsync();

            for (int i = 0; i < stats.Count; i++)
            {
                stats[i].Color = colors[i % colors.Length];
            }

            return stats;
        }

        public async Task<IEnumerable<EnrollmentStatistic>> GetEnrollmentStatisticsAsync()
        {
            return await _context.Students
                .GroupBy(s => s.EnrollmentDate.Year)
                .Select(g => new EnrollmentStatistic
                {
                    Year = g.Key,
                    Count = g.Count()
                })
                .OrderBy(e => e.Year)
                .ToListAsync();
        }
    }
}
