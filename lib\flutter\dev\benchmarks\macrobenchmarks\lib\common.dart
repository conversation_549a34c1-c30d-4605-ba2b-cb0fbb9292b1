// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

const String kCullOpacityRouteName = '/cull_opacity';
const String kCubicBezierRouteName = '/cubic_bezier';
const String kBackdropFilterRouteName = '/backdrop_filter';
const String kPostBackdropFilterRouteName = '/post_backdrop_filter';
const String kSimpleAnimationRouteName = '/simple_animation';
const String kPictureCacheRouteName = '/picture_cache';
const String kPictureCacheComplexityScoringRouteName = '/picture_cache_complexity_scoring';
const String kLargeImageChangerRouteName = '/large_image_changer';
const String kLargeImagesRouteName = '/large_images';
const String kPathTessellationRouteName = '/path_tessellation';
const String kTextRouteName = '/text';
const String kVeryLongPictureScrollingRouteName = '/very_long_picture_scrolling';
const String kFullscreenTextRouteName = '/fullscreen_text';
const String kAnimatedPlaceholderRouteName = '/animated_placeholder';
const String kClipperCacheRouteName = '/clipper_cache';
const String kColorFilterAndFadeRouteName = '/color_filter_and_fade';
const String kColorFilterCacheRouteName = '/color_filter_cache';
const String kColorFilterWithUnstableChildName = '/color_filter_with_unstable_child';
const String kFadingChildAnimationRouteName = '/fading_child_animation';
const String kImageFilteredTransformAnimationRouteName = '/imagefiltered_transform_animation';
const String kMultiWidgetConstructionRouteName = '/multi_widget_construction';
const String kHeavyGridViewRouteName = '/heavy_gridview';
const String kRasterCacheUseMemory = '/raster_cache_use_memory';
const String kShaderMaskCacheRouteName = '/shader_mask_cache';
const String kSimpleScrollRouteName = '/simple_scroll';
const String kAnimationWithMicrotasksRouteName = '/animation_with_microtasks';
const String kAnimatedImageRouteName = '/animated_image';
const String kOpacityPeepholeRouteName = '/opacity_peephole';
const String kGradientPerfRouteName = '/gradient_perf';
const String kAnimatedComplexOpacityPerfRouteName = '/animated_complex_opacity';
const String kAnimatedComplexImageFilteredPerfRouteName = '/animated_complex_image_filtered';
const String kListTextLayoutRouteName = '/list_text_layout';
const String kAnimatedBlurBackdropFilter = '/animated_blur_backdrop_filter';
const String kSlidersRouteName = '/sliders';
const String kDrawPointsPageRougeName = '/draw_points';
const String kDrawVerticesPageRouteName = '/draw_vertices';
const String kDrawAtlasPageRouteName = '/draw_atlas';
const String kAnimatedAdvancedBlend = '/animated_advanced_blend';
const String kRRectBlurRouteName = '/rrect_blur';

const String kOpacityPeepholeOneRectRouteName = '$kOpacityPeepholeRouteName/one_big_rect';
const String kOpacityPeepholeColumnOfOpacityRouteName =
    '$kOpacityPeepholeRouteName/column_of_opacity';
const String kOpacityPeepholeOpacityOfCachedChildRouteName =
    '$kOpacityPeepholeRouteName/opacity_of_cached_child';
const String kOpacityPeepholeOpacityOfColumnRouteName =
    '$kOpacityPeepholeRouteName/opacity_of_column';
const String kOpacityPeepholeGridOfOpacityRouteName = '$kOpacityPeepholeRouteName/grid_of_opacity';
const String kOpacityPeepholeOpacityOfGridRouteName = '$kOpacityPeepholeRouteName/opacity_of_grid';
const String kOpacityPeepholeOpacityOfColOfRowsRouteName =
    '$kOpacityPeepholeRouteName/opacity_of_col_of_rows';
const String kOpacityPeepholeFadeTransitionTextRouteName =
    '$kOpacityPeepholeRouteName/fade_transition_text';
const String kOpacityPeepholeGridOfRectsWithAlphaRouteName =
    '$kOpacityPeepholeRouteName/grid_of_rects_with_alpha';
const String kOpacityPeepholeGridOfAlphaSaveLayerRectsRouteName =
    '$kOpacityPeepholeRouteName/grid_of_alpha_savelayer_rects';
const String kOpacityPeepholeColumnOfAlphaSaveLayerRowsOfRectsRouteName =
    '$kOpacityPeepholeRouteName/column_of_alpha_save_layer_rows_of_rects';

const String kGradientPerfRecreateDynamicRouteName = '$kGradientPerfRouteName/recreate_dynamic';
const String kGradientPerfRecreateConsistentRouteName =
    '$kGradientPerfRouteName/recreate_consistent';
const String kGradientPerfStaticConsistentRouteName = '$kGradientPerfRouteName/static_consistent';

const String kScrollableName = '/macrobenchmark_listview';
const String kOpacityScrollableName = '$kOpacityPeepholeRouteName/listview';
const String kGradientPerfScrollableName = '$kGradientPerfRouteName/listview';
