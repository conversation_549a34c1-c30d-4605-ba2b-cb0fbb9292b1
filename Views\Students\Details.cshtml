@model StudentManagementSystem.Models.Student
@{
    ViewData["Title"] = "Student Details";
}

<div class="row justify-content-center">
    <div class="col-lg-10">
        <!-- Header -->
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
                <a href="/Students" class="btn btn-outline-secondary me-3">
                    <i class="bi bi-arrow-left"></i>
                </a>
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-person-fill text-info me-2"></i>
                        Student Details
                    </h2>
                    <p class="text-muted mb-0">Complete student information</p>
                </div>
            </div>
            <div class="d-flex gap-2">
                <a href="/Students/Edit/@Model.Id" class="btn btn-gradient-warning">
                    <i class="bi bi-pencil-fill me-2"></i>
                    Edit Student
                </a>
                <a href="/Students/Delete/@Model.Id" class="btn btn-gradient-danger">
                    <i class="bi bi-trash-fill me-2"></i>
                    Delete
                </a>
            </div>
        </div>

        <div class="row g-4">
            <!-- Profile Card -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center p-4">
                        <div class="mb-4">
                            @if (!string.IsNullOrEmpty(Model.ProfilePhotoPath))
                            {
                                <img src="~/@Model.ProfilePhotoPath" alt="@Model.FullName" 
                                     class="rounded-circle mb-3" width="150" height="150" style="object-fit: cover;">
                            }
                            else
                            {
                                <div class="rounded-circle bg-gradient-primary d-flex align-items-center justify-content-center text-white fw-bold mx-auto mb-3" 
                                     style="width: 150px; height: 150px; font-size: 3rem;">
                                    @Model.FullName.Substring(0, 1).ToUpper()
                                </div>
                            }
                        </div>
                        
                        <h4 class="fw-bold mb-2">@Model.FullName</h4>
                        <p class="text-muted mb-3">@Model.StudentId</p>
                        
                        <div class="d-flex justify-content-center mb-3">
                            <span class="badge @(Model.IsActive ? "bg-success" : "bg-secondary") fs-6 px-3 py-2">
                                <i class="bi bi-@(Model.IsActive ? "check" : "x")-circle-fill me-1"></i>
                                @(Model.IsActive ? "Active" : "Inactive")
                            </span>
                        </div>

                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <div class="fs-4 fw-bold text-primary">@Model.Age</div>
                                    <div class="text-muted small">Years Old</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="fs-4 fw-bold text-success">@Model.EnrollmentYear</div>
                                <div class="text-muted small">Enrolled</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Information Cards -->
            <div class="col-lg-8">
                <!-- Personal Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-fill me-2"></i>
                            Personal Information
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-envelope-fill text-primary fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="text-muted small">Email Address</div>
                                        <div class="fw-semibold">@Model.Email</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-telephone-fill text-success fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="text-muted small">Phone Number</div>
                                        <div class="fw-semibold">@(Model.Phone ?? "Not provided")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-calendar-fill text-warning fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="text-muted small">Date of Birth</div>
                                        <div class="fw-semibold">@Model.DateOfBirth.ToString("MMMM dd, yyyy")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-gender-ambiguous text-info fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="text-muted small">Gender</div>
                                        <div class="fw-semibold">@Model.Gender</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Academic Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-gradient-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-mortarboard-fill me-2"></i>
                            Academic Information
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-building-fill text-primary fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="text-muted small">Department</div>
                                        <div class="fw-semibold">@Model.Department?.Name</div>
                                        <div class="text-muted small">@Model.Department?.Code</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-calendar-check-fill text-success fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="text-muted small">Enrollment Date</div>
                                        <div class="fw-semibold">@Model.EnrollmentDate.ToString("MMMM dd, yyyy")</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-gradient-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear-fill me-2"></i>
                            System Information
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-plus-circle-fill text-primary fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="text-muted small">Created Date</div>
                                        <div class="fw-semibold">@Model.CreatedDate.ToString("MMMM dd, yyyy")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-pencil-fill text-warning fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="text-muted small">Last Updated</div>
                                        <div class="fw-semibold">@Model.UpdatedDate.ToString("MMMM dd, yyyy")</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-gradient-primary {
        background: linear-gradient(135deg, #6366F1, #4F46E5) !important;
    }
    
    .bg-gradient-success {
        background: linear-gradient(135deg, #10B981, #059669) !important;
    }
    
    .bg-gradient-info {
        background: linear-gradient(135deg, #06B6D4, #0891B2) !important;
    }
    
    .bg-gradient-warning {
        background: linear-gradient(135deg, #F59E0B, #D97706) !important;
    }

    .btn-gradient-warning {
        background: linear-gradient(135deg, #F59E0B, #D97706);
        border: none;
        color: white;
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-gradient-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
        color: white;
    }

    .btn-gradient-danger {
        background: linear-gradient(135deg, #EF4444, #DC2626);
        border: none;
        color: white;
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-gradient-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        color: white;
    }

    .card-header {
        border-radius: 1rem 1rem 0 0 !important;
    }
</style>
