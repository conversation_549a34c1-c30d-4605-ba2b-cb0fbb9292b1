# See Dependabot documentation for all configuration options:
# https://help.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    groups:
      all-github-actions:
        patterns: [ "*" ]
    reviewers:
      - "christopher<PERSON>jino"
      - "jmagman"
    labels:
      - "team"
      - "team-infra"
      - "autosubmit"
    # Updating patch versions for "github-actions" is too chatty.
    # See https://github.com/flutter/flutter/issues/158350.
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-patch"]
