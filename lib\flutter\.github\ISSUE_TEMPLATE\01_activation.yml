name: I am having difficulty installing Flutter or getting it to work
description: You have run into problems while downloading or installing Flutter, or the
  "flutter" tool is crashing, or you are running into some other issue before even
  being able to use "flutter run".
body:
  - type: markdown
    attributes:
      value: |
        Thank you for using Flutter!

        If you are looking for support, please check out our documentation
        or consider asking a question on Stack Overflow:

          - https://flutter.dev/
          - https://api.flutter.dev/
          - https://stackoverflow.com/questions/tagged/flutter?sort=frequent

        If you have found a bug or if our documentation doesn't have an answer
        to what you're looking for, then fill out the template below.
  - type: markdown
    attributes:
      value: |
        Before filling the form fields, please consider the following:
        - Ensure that you have searched the [existing issues](https://github.com/flutter/flutter/issues)
        - Read the [guide to filing a bug](https://flutter.dev/docs/resources/bug-reports)
  - type: textarea
    attributes:
      label: Steps to reproduce
      description: Please tell us exactly how to reproduce the problem you are running into.
      placeholder: |
        1. ...
        2. ...
        3. ...
    validations:
      required: true
  - type: textarea
    attributes:
      label: Actual results
      description: Please tell us what is actually happening
    validations:
      required: true
  - type: textarea
    attributes:
      label: Logs
      description: |
        Include the full logs of the commands you are running between the lines
        with the backticks below. If you are running any `flutter` commands,
        please include the output of running them with `--verbose`; for example,
        the output of running `flutter --verbose create foo`.

        If the logs are too large to be uploaded to Github, you may upload
        them as a `txt` file or use online tools like https://pastebin.com to
        share it.

        Note: Please do not upload screenshots of text. Instead, use code blocks
        or the above mentioned ways to upload logs.
      value: |
        <details open>
        <summary>Logs</summary>

        ```console

        <!-- Paste your logs here -->

        ```

        </details>
  - type: textarea
    attributes:
      label: Flutter Doctor output
      description: |
        Please provide the full output of running `flutter doctor -v`
      value: |
        <details open>
        <summary>Doctor output</summary>

        ```console

        <!-- Paste your output here -->

        ```

        </details>
    validations:
      required: true
