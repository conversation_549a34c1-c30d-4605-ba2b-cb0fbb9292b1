using Microsoft.EntityFrameworkCore;
using StudentManagementSystem.Models;

namespace StudentManagementSystem.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Student> Students { get; set; }
        public DbSet<Department> Departments { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Student entity
            modelBuilder.Entity<Student>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Phone).HasMaxLength(20);
                entity.Property(e => e.StudentId).HasMaxLength(20);
                entity.Property(e => e.ProfilePhotoPath).HasMaxLength(500);
                
                // Configure relationship
                entity.HasOne(s => s.Department)
                      .WithMany(d => d.Students)
                      .HasForeignKey(s => s.DepartmentId)
                      .OnDelete(DeleteBehavior.Restrict);

                // Create index for better performance
                entity.HasIndex(e => e.Email).IsUnique();
                entity.HasIndex(e => e.StudentId).IsUnique();
            });

            // Configure Department entity
            modelBuilder.Entity<Department>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(10);
                entity.Property(e => e.Description).HasMaxLength(500);
                
                // Create index for better performance
                entity.HasIndex(e => e.Code).IsUnique();
            });

            // Seed initial data
            modelBuilder.Entity<Department>().HasData(
                new Department { Id = 1, Name = "Computer Science", Code = "CS", Description = "Computer Science and Engineering", CreatedDate = DateTime.Now },
                new Department { Id = 2, Name = "Business Administration", Code = "BA", Description = "Business and Management Studies", CreatedDate = DateTime.Now },
                new Department { Id = 3, Name = "Electrical Engineering", Code = "EE", Description = "Electrical and Electronics Engineering", CreatedDate = DateTime.Now },
                new Department { Id = 4, Name = "Mechanical Engineering", Code = "ME", Description = "Mechanical Engineering", CreatedDate = DateTime.Now },
                new Department { Id = 5, Name = "Mathematics", Code = "MATH", Description = "Mathematics and Statistics", CreatedDate = DateTime.Now }
            );
        }
    }
}
