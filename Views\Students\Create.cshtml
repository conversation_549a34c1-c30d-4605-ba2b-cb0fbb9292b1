@model StudentManagementSystem.Models.ViewModels.StudentViewModel
@{
    ViewData["Title"] = "Add New Student";
}

<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Header -->
        <div class="d-flex align-items-center mb-4">
            <a href="/Students" class="btn btn-outline-secondary me-3">
                <i class="bi bi-arrow-left"></i>
            </a>
            <div>
                <h2 class="mb-1">
                    <i class="bi bi-person-plus-fill text-primary me-2"></i>
                    Add New Student
                </h2>
                <p class="text-muted mb-0">Fill in the student information below</p>
            </div>
        </div>

        <form asp-action="Create" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

            <!-- Personal Information Card -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-fill me-2"></i>
                        Personal Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label asp-for="FullName" class="form-label fw-semibold">
                                <i class="bi bi-person me-1"></i>
                                Full Name *
                            </label>
                            <input asp-for="FullName" class="form-control form-control-lg" placeholder="Enter full name">
                            <span asp-validation-for="FullName" class="text-danger small"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="Email" class="form-label fw-semibold">
                                <i class="bi bi-envelope me-1"></i>
                                Email Address *
                            </label>
                            <input asp-for="Email" class="form-control form-control-lg" placeholder="Enter email address">
                            <span asp-validation-for="Email" class="text-danger small"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="Phone" class="form-label fw-semibold">
                                <i class="bi bi-telephone me-1"></i>
                                Phone Number
                            </label>
                            <input asp-for="Phone" class="form-control form-control-lg" placeholder="Enter phone number">
                            <span asp-validation-for="Phone" class="text-danger small"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="DateOfBirth" class="form-label fw-semibold">
                                <i class="bi bi-calendar me-1"></i>
                                Date of Birth *
                            </label>
                            <input asp-for="DateOfBirth" class="form-control form-control-lg" type="date">
                            <span asp-validation-for="DateOfBirth" class="text-danger small"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="Gender" class="form-label fw-semibold">
                                <i class="bi bi-gender-ambiguous me-1"></i>
                                Gender *
                            </label>
                            <select asp-for="Gender" class="form-select form-select-lg" asp-items="Html.GetEnumSelectList<StudentManagementSystem.Models.Gender>()">
                                <option value="">Select Gender</option>
                            </select>
                            <span asp-validation-for="Gender" class="text-danger small"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="ProfilePhoto" class="form-label fw-semibold">
                                <i class="bi bi-camera me-1"></i>
                                Profile Photo
                            </label>
                            <input asp-for="ProfilePhoto" class="form-control form-control-lg" type="file" accept="image/*">
                            <div class="form-text">Accepted formats: JPG, PNG, GIF (Max 5MB)</div>
                            <span asp-validation-for="ProfilePhoto" class="text-danger small"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Academic Information Card -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-gradient-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-mortarboard-fill me-2"></i>
                        Academic Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label asp-for="StudentId" class="form-label fw-semibold">
                                <i class="bi bi-card-text me-1"></i>
                                Student ID
                            </label>
                            <input asp-for="StudentId" class="form-control form-control-lg" placeholder="Enter student ID">
                            <span asp-validation-for="StudentId" class="text-danger small"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="DepartmentId" class="form-label fw-semibold">
                                <i class="bi bi-building me-1"></i>
                                Department *
                            </label>
                            <select asp-for="DepartmentId" class="form-select form-select-lg" asp-items="Model.Departments">
                                <option value="">Select Department</option>
                            </select>
                            <span asp-validation-for="DepartmentId" class="text-danger small"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="EnrollmentDate" class="form-label fw-semibold">
                                <i class="bi bi-calendar-check me-1"></i>
                                Enrollment Date *
                            </label>
                            <input asp-for="EnrollmentDate" class="form-control form-control-lg" type="date">
                            <span asp-validation-for="EnrollmentDate" class="text-danger small"></span>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mt-4 pt-2">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" checked>
                                <label asp-for="IsActive" class="form-check-label fw-semibold">
                                    <i class="bi bi-toggle-on me-1"></i>
                                    Active Student
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex gap-3 justify-content-end">
                        <a href="/Students" class="btn btn-outline-secondary btn-lg">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-gradient-primary btn-lg">
                            <i class="bi bi-check-circle me-2"></i>
                            Create Student
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
    .bg-gradient-primary {
        background: linear-gradient(135deg, #6366F1, #4F46E5) !important;
    }
    
    .bg-gradient-success {
        background: linear-gradient(135deg, #10B981, #059669) !important;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    }

    .form-control-lg,
    .form-select-lg {
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
    }

    .card-header {
        border-radius: 1rem 1rem 0 0 !important;
    }
</style>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Preview uploaded image
        document.querySelector('input[type="file"]').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // You can add image preview functionality here
                    console.log('Image selected:', file.name);
                };
                reader.readAsDataURL(file);
            }
        });

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
}
