# 🎓 Student Management System

A modern, responsive Student Management System built with ASP.NET Core MVC, Entity Framework Core, and SQLite. Features a beautiful, colorful UI with sidebar navigation and comprehensive student management capabilities.

## ✨ Features

### 🎨 Modern Design
- **Responsive Design**: Mobile-first approach with Bootstrap 5
- **Colorful UI**: Beautiful, compatible color scheme with gradients
- **Sidebar Navigation**: Collapsible sidebar with smooth animations
- **Modern Cards**: Clean, shadow-enhanced card layouts
- **Interactive Elements**: Hover effects and smooth transitions

### 👥 Student Management
- **CRUD Operations**: Create, Read, Update, Delete student records
- **Profile Photos**: Upload and manage student profile pictures
- **Advanced Search**: Live search by name or email
- **Filtering**: Filter by department and enrollment year
- **Pagination**: Efficient data browsing with pagination

### 🏢 Department Management
- **Department CRUD**: Manage academic departments
- **Student Tracking**: View student count per department
- **Statistics**: Department enrollment statistics

### 📊 Dashboard & Analytics
- **Overview Statistics**: Total students, active students, new enrollments
- **Visual Charts**: Department distribution and enrollment trends
- **Quick Actions**: Fast access to common operations

### 🔧 Technical Features
- **Entity Framework Core**: Code-first approach with migrations
- **SQLite Database**: Lightweight, portable database
- **File Upload**: Secure image upload with validation
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern Architecture**: Clean, maintainable code structure

## 🚀 Getting Started

### Prerequisites
- .NET 8.0 SDK or later
- Visual Studio 2022 or VS Code
- Git (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd StudentManagementSystem
   ```

2. **Restore packages**
   ```bash
   dotnet restore
   ```

3. **Create the database**
   ```bash
   dotnet ef database update
   ```
   *Note: The database will be automatically created with sample data on first run*

4. **Run the application**
   ```bash
   dotnet run
   ```

5. **Open in browser**
   Navigate to `https://localhost:5001` or `http://localhost:5000`

## 📁 Project Structure

```
StudentManagementSystem/
├── Controllers/           # MVC Controllers
│   ├── HomeController.cs
│   ├── StudentsController.cs
│   └── DepartmentsController.cs
├── Models/               # Data Models
│   ├── Student.cs
│   ├── Department.cs
│   └── ViewModels/
├── Views/                # Razor Views
│   ├── Home/
│   ├── Students/
│   ├── Departments/
│   └── Shared/
├── Services/             # Business Logic
│   ├── StudentService.cs
│   ├── DepartmentService.cs
│   └── FileService.cs
├── Data/                 # Database Context
│   ├── ApplicationDbContext.cs
│   └── SeedData.cs
└── wwwroot/             # Static Files
    ├── css/
    ├── js/
    └── uploads/
```

## 🎨 Color Scheme

The application uses a modern, vibrant color palette:

- **Primary**: `#6366F1` (Indigo)
- **Secondary**: `#EC4899` (Pink)
- **Success**: `#10B981` (Emerald)
- **Warning**: `#F59E0B` (Amber)
- **Danger**: `#EF4444` (Red)
- **Info**: `#06B6D4` (Cyan)

## 📱 Responsive Design

- **Desktop**: Full sidebar with expanded navigation
- **Tablet**: Collapsible sidebar with icons and text
- **Mobile**: Hidden sidebar with overlay toggle

## 🔧 Configuration

### Database Connection
Update `appsettings.json` to modify the database connection:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=StudentManagement.db"
  }
}
```

### File Upload Settings
Configure file upload limits in `appsettings.json`:
```json
{
  "FileUpload": {
    "MaxFileSize": 5242880,
    "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif"],
    "UploadPath": "wwwroot/uploads/profiles"
  }
}
```

## 🚀 Deployment

### Local Deployment
1. Build the application: `dotnet build --configuration Release`
2. Publish: `dotnet publish --configuration Release`
3. Run: `dotnet StudentManagementSystem.dll`

### IIS Deployment
1. Publish to folder
2. Copy to IIS wwwroot
3. Configure application pool for .NET 8.0
4. Ensure proper permissions for uploads folder

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Bootstrap 5 for the responsive framework
- Bootstrap Icons for the beautiful icons
- Entity Framework Core for data access
- ASP.NET Core MVC for the web framework

## 📞 Support

For support, please open an issue in the GitHub repository or contact the development team.

---

**Built with ❤️ using ASP.NET Core MVC**
