{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6924"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UigFIF298E0wW4Yxf+0SxBWIFiPhGIqcTlCVj65BrmQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 04:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UigFIF298E0wW4Yxf+0SxBWIFiPhGIqcTlCVj65BrmQ="}]}, {"Route": "css/site.qibwuup2ps.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6924"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UigFIF298E0wW4Yxf+0SxBWIFiPhGIqcTlCVj65BrmQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 04:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qibwuup2ps"}, {"Name": "integrity", "Value": "sha256-UigFIF298E0wW4Yxf+0SxBWIFiPhGIqcTlCVj65BrmQ="}, {"Name": "label", "Value": "css/site.css"}]}]}