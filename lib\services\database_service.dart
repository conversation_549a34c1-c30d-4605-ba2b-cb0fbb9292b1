import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';
import '../models/task.dart';

/// Service class for managing local database operations
/// Uses SQLite to store tasks locally on the device
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  /// Get the database instance, creating it if it doesn't exist
  Future<Database> get database async {
    if (_database != null) return _database!;

    // For web platform, we need to handle SQLite differently
    if (kIsWeb) {
      throw UnsupportedError(
        'SQLite is not supported on web. Consider using IndexedDB or other web storage solutions.'
      );
    }

    _database = await _initDatabase();
    return _database!;
  }

  /// Initialize the database and create tables
  Future<Database> _initDatabase() async {
    // Get the path to store the database
    String path = join(await getDatabasesPath(), 'tasks.db');
    
    // Open the database and create the tasks table
    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) {
        return db.execute(
          '''CREATE TABLE tasks(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            isCompleted INTEGER NOT NULL DEFAULT 0,
            dueDate TEXT,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL
          )''',
        );
      },
    );
  }

  /// Insert a new task into the database
  Future<int> insertTask(Task task) async {
    final db = await database;
    
    // Update the task's timestamps
    task.createdAt = DateTime.now();
    task.updatedAt = DateTime.now();
    
    // Insert the task and return the generated ID
    int id = await db.insert('tasks', task.toMap());
    return id;
  }

  /// Get all tasks from the database
  Future<List<Task>> getAllTasks() async {
    final db = await database;
    
    // Query all tasks ordered by creation date (newest first)
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      orderBy: 'createdAt DESC',
    );

    // Convert the query results to Task objects
    return List.generate(maps.length, (i) {
      return Task.fromMap(maps[i]);
    });
  }

  /// Get a specific task by ID
  Future<Task?> getTaskById(int id) async {
    final db = await database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Task.fromMap(maps.first);
    }
    return null;
  }

  /// Update an existing task
  Future<int> updateTask(Task task) async {
    final db = await database;
    
    // Update the timestamp
    task.updatedAt = DateTime.now();
    
    // Update the task in the database
    return await db.update(
      'tasks',
      task.toMap(),
      where: 'id = ?',
      whereArgs: [task.id],
    );
  }

  /// Delete a task by ID
  Future<int> deleteTask(int id) async {
    final db = await database;
    
    return await db.delete(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get completed tasks
  Future<List<Task>> getCompletedTasks() async {
    final db = await database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'isCompleted = ?',
      whereArgs: [1],
      orderBy: 'updatedAt DESC',
    );

    return List.generate(maps.length, (i) {
      return Task.fromMap(maps[i]);
    });
  }

  /// Get pending (incomplete) tasks
  Future<List<Task>> getPendingTasks() async {
    final db = await database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'isCompleted = ?',
      whereArgs: [0],
      orderBy: 'dueDate ASC, createdAt DESC',
    );

    return List.generate(maps.length, (i) {
      return Task.fromMap(maps[i]);
    });
  }

  /// Get overdue tasks
  Future<List<Task>> getOverdueTasks() async {
    final db = await database;
    final now = DateTime.now().toIso8601String();
    
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'isCompleted = ? AND dueDate < ?',
      whereArgs: [0, now],
      orderBy: 'dueDate ASC',
    );

    return List.generate(maps.length, (i) {
      return Task.fromMap(maps[i]);
    });
  }

  /// Close the database connection
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
