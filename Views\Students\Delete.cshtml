@model StudentManagementSystem.Models.Student
@{
    ViewData["Title"] = "Delete Student";
}

<div class="row justify-content-center">
    <div class="col-lg-6">
        <!-- Header -->
        <div class="d-flex align-items-center mb-4">
            <a href="/Students" class="btn btn-outline-secondary me-3">
                <i class="bi bi-arrow-left"></i>
            </a>
            <div>
                <h2 class="mb-1">
                    <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                    Delete Student
                </h2>
                <p class="text-muted mb-0">This action cannot be undone</p>
            </div>
        </div>

        <!-- Confirmation Card -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-trash-fill me-2"></i>
                    Confirm Deletion
                </h5>
            </div>
            <div class="card-body p-4">
                <!-- Student Info -->
                <div class="text-center mb-4">
                    @if (!string.IsNullOrEmpty(Model.ProfilePhotoPath))
                    {
                        <img src="~/@Model.ProfilePhotoPath" alt="@Model.FullName" 
                             class="rounded-circle mb-3" width="100" height="100" style="object-fit: cover;">
                    }
                    else
                    {
                        <div class="rounded-circle bg-gradient-primary d-flex align-items-center justify-content-center text-white fw-bold mx-auto mb-3" 
                             style="width: 100px; height: 100px; font-size: 2rem;">
                            @Model.FullName.Substring(0, 1).ToUpper()
                        </div>
                    }
                    <h4 class="fw-bold">@Model.FullName</h4>
                    <p class="text-muted">@Model.StudentId</p>
                </div>

                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Warning!</strong> You are about to permanently delete this student record. This action cannot be undone.
                </div>

                <!-- Student Details Summary -->
                <div class="row g-3 mb-4">
                    <div class="col-6">
                        <div class="text-center p-3 bg-light rounded-3">
                            <div class="text-muted small">Email</div>
                            <div class="fw-semibold">@Model.Email</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-light rounded-3">
                            <div class="text-muted small">Department</div>
                            <div class="fw-semibold">@Model.Department?.Name</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-light rounded-3">
                            <div class="text-muted small">Enrollment Date</div>
                            <div class="fw-semibold">@Model.EnrollmentDate.ToString("MMM yyyy")</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-light rounded-3">
                            <div class="text-muted small">Status</div>
                            <div class="fw-semibold">
                                <span class="badge @(Model.IsActive ? "bg-success" : "bg-secondary")">
                                    @(Model.IsActive ? "Active" : "Inactive")
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Confirmation Form -->
                <form asp-action="Delete" method="post">
                    <input asp-for="Id" type="hidden" />
                    <div class="d-flex gap-3 justify-content-end">
                        <a href="/Students" class="btn btn-outline-secondary btn-lg">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-gradient-danger btn-lg">
                            <i class="bi bi-trash-fill me-2"></i>
                            Yes, Delete Student
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-gradient-danger {
        background: linear-gradient(135deg, #EF4444, #DC2626) !important;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #6366F1, #4F46E5) !important;
    }

    .btn-gradient-danger {
        background: linear-gradient(135deg, #EF4444, #DC2626);
        border: none;
        color: white;
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-gradient-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        color: white;
    }

    .card-header {
        border-radius: 1rem 1rem 0 0 !important;
    }
</style>
