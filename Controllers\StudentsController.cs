using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using StudentManagementSystem.Models;
using StudentManagementSystem.Models.ViewModels;
using StudentManagementSystem.Services;

namespace StudentManagementSystem.Controllers
{
    public class StudentsController : Controller
    {
        private readonly IStudentService _studentService;
        private readonly IDepartmentService _departmentService;
        private readonly IFileService _fileService;

        public StudentsController(IStudentService studentService, IDepartmentService departmentService, IFileService fileService)
        {
            _studentService = studentService;
            _departmentService = departmentService;
            _fileService = fileService;
        }

        // GET: Students
        public async Task<IActionResult> Index(string? searchTerm, int? departmentId, int? enrollmentYear, int page = 1)
        {
            var viewModel = await _studentService.GetFilteredStudentsAsync(searchTerm, departmentId, enrollmentYear, page, 10);
            
            // Populate dropdowns
            var departments = await _departmentService.GetAllDepartmentsAsync();
            viewModel.Departments = departments.Select(d => new SelectListItem
            {
                Value = d.Id.ToString(),
                Text = d.Name,
                Selected = d.Id == departmentId
            });

            var enrollmentYears = await _studentService.GetEnrollmentYearsAsync();
            viewModel.EnrollmentYears = enrollmentYears.Select(y => new SelectListItem
            {
                Value = y.ToString(),
                Text = y.ToString(),
                Selected = y == enrollmentYear
            });

            return View(viewModel);
        }

        // GET: Students/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var student = await _studentService.GetStudentByIdAsync(id);
            if (student == null)
            {
                return NotFound();
            }

            return View(student);
        }

        // GET: Students/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new StudentViewModel();
            await PopulateDepartmentsDropdown(viewModel);
            return View(viewModel);
        }

        // POST: Students/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(StudentViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                // Check for duplicate email
                if (await _studentService.EmailExistsAsync(viewModel.Email))
                {
                    ModelState.AddModelError("Email", "Email already exists");
                }

                // Check for duplicate student ID if provided
                if (!string.IsNullOrEmpty(viewModel.StudentId) && 
                    await _studentService.StudentIdExistsAsync(viewModel.StudentId))
                {
                    ModelState.AddModelError("StudentId", "Student ID already exists");
                }

                if (ModelState.IsValid)
                {
                    var student = new Student
                    {
                        FullName = viewModel.FullName,
                        Email = viewModel.Email,
                        Phone = viewModel.Phone,
                        DateOfBirth = viewModel.DateOfBirth,
                        Gender = viewModel.Gender,
                        DepartmentId = viewModel.DepartmentId,
                        EnrollmentDate = viewModel.EnrollmentDate,
                        StudentId = viewModel.StudentId,
                        IsActive = viewModel.IsActive
                    };

                    // Handle file upload
                    if (viewModel.ProfilePhoto != null)
                    {
                        student.ProfilePhotoPath = await _fileService.SaveFileAsync(viewModel.ProfilePhoto, "profiles");
                    }

                    await _studentService.CreateStudentAsync(student);
                    TempData["SuccessMessage"] = "Student created successfully!";
                    return RedirectToAction(nameof(Index));
                }
            }

            await PopulateDepartmentsDropdown(viewModel);
            return View(viewModel);
        }

        // GET: Students/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var student = await _studentService.GetStudentByIdAsync(id);
            if (student == null)
            {
                return NotFound();
            }

            var viewModel = new StudentViewModel
            {
                Id = student.Id,
                FullName = student.FullName,
                Email = student.Email,
                Phone = student.Phone,
                DateOfBirth = student.DateOfBirth,
                Gender = student.Gender,
                DepartmentId = student.DepartmentId,
                EnrollmentDate = student.EnrollmentDate,
                StudentId = student.StudentId,
                IsActive = student.IsActive,
                ProfilePhotoPath = student.ProfilePhotoPath
            };

            await PopulateDepartmentsDropdown(viewModel);
            return View(viewModel);
        }

        // POST: Students/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, StudentViewModel viewModel)
        {
            if (id != viewModel.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                // Check for duplicate email
                if (await _studentService.EmailExistsAsync(viewModel.Email, id))
                {
                    ModelState.AddModelError("Email", "Email already exists");
                }

                // Check for duplicate student ID if provided
                if (!string.IsNullOrEmpty(viewModel.StudentId) && 
                    await _studentService.StudentIdExistsAsync(viewModel.StudentId, id))
                {
                    ModelState.AddModelError("StudentId", "Student ID already exists");
                }

                if (ModelState.IsValid)
                {
                    var student = await _studentService.GetStudentByIdAsync(id);
                    if (student == null)
                    {
                        return NotFound();
                    }

                    // Update properties
                    student.FullName = viewModel.FullName;
                    student.Email = viewModel.Email;
                    student.Phone = viewModel.Phone;
                    student.DateOfBirth = viewModel.DateOfBirth;
                    student.Gender = viewModel.Gender;
                    student.DepartmentId = viewModel.DepartmentId;
                    student.EnrollmentDate = viewModel.EnrollmentDate;
                    student.StudentId = viewModel.StudentId;
                    student.IsActive = viewModel.IsActive;

                    // Handle file upload
                    if (viewModel.ProfilePhoto != null)
                    {
                        // Delete old photo
                        if (!string.IsNullOrEmpty(student.ProfilePhotoPath))
                        {
                            await _fileService.DeleteFileAsync(student.ProfilePhotoPath);
                        }
                        
                        student.ProfilePhotoPath = await _fileService.SaveFileAsync(viewModel.ProfilePhoto, "profiles");
                    }

                    await _studentService.UpdateStudentAsync(student);
                    TempData["SuccessMessage"] = "Student updated successfully!";
                    return RedirectToAction(nameof(Index));
                }
            }

            await PopulateDepartmentsDropdown(viewModel);
            return View(viewModel);
        }

        // GET: Students/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var student = await _studentService.GetStudentByIdAsync(id);
            if (student == null)
            {
                return NotFound();
            }

            return View(student);
        }

        // POST: Students/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var student = await _studentService.GetStudentByIdAsync(id);
            if (student != null)
            {
                // Delete profile photo
                if (!string.IsNullOrEmpty(student.ProfilePhotoPath))
                {
                    await _fileService.DeleteFileAsync(student.ProfilePhotoPath);
                }

                await _studentService.DeleteStudentAsync(id);
                TempData["SuccessMessage"] = "Student deleted successfully!";
            }

            return RedirectToAction(nameof(Index));
        }

        // AJAX endpoint for live search
        [HttpGet]
        public async Task<IActionResult> Search(string term)
        {
            var result = await _studentService.GetFilteredStudentsAsync(term, null, null, 1, 10);
            var students = result.Students.Select(s => new
            {
                id = s.Id,
                fullName = s.FullName,
                email = s.Email,
                department = s.Department?.Name,
                profilePhoto = _fileService.GetFileUrl(s.ProfilePhotoPath)
            });

            return Json(students);
        }

        private async Task PopulateDepartmentsDropdown(StudentViewModel viewModel)
        {
            var departments = await _departmentService.GetAllDepartmentsAsync();
            viewModel.Departments = departments.Select(d => new SelectListItem
            {
                Value = d.Id.ToString(),
                Text = d.Name
            });
        }
    }
}
