using Microsoft.AspNetCore.Mvc;
using StudentManagementSystem.Models;
using StudentManagementSystem.Services;

namespace StudentManagementSystem.Controllers
{
    public class DepartmentsController : Controller
    {
        private readonly IDepartmentService _departmentService;

        public DepartmentsController(IDepartmentService departmentService)
        {
            _departmentService = departmentService;
        }

        // GET: Departments
        public async Task<IActionResult> Index()
        {
            var departments = await _departmentService.GetAllDepartmentsAsync();
            return View(departments);
        }

        // GET: Departments/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var department = await _departmentService.GetDepartmentByIdAsync(id);
            if (department == null)
            {
                return NotFound();
            }

            return View(department);
        }

        // GET: Departments/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Departments/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Department department)
        {
            if (ModelState.IsValid)
            {
                // Check for duplicate code
                if (await _departmentService.DepartmentCodeExistsAsync(department.Code))
                {
                    ModelState.AddModelError("Code", "Department code already exists");
                }

                // Check for duplicate name
                if (await _departmentService.DepartmentNameExistsAsync(department.Name))
                {
                    ModelState.AddModelError("Name", "Department name already exists");
                }

                if (ModelState.IsValid)
                {
                    await _departmentService.CreateDepartmentAsync(department);
                    TempData["SuccessMessage"] = "Department created successfully!";
                    return RedirectToAction(nameof(Index));
                }
            }

            return View(department);
        }

        // GET: Departments/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var department = await _departmentService.GetDepartmentByIdAsync(id);
            if (department == null)
            {
                return NotFound();
            }

            return View(department);
        }

        // POST: Departments/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Department department)
        {
            if (id != department.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                // Check for duplicate code
                if (await _departmentService.DepartmentCodeExistsAsync(department.Code, id))
                {
                    ModelState.AddModelError("Code", "Department code already exists");
                }

                // Check for duplicate name
                if (await _departmentService.DepartmentNameExistsAsync(department.Name, id))
                {
                    ModelState.AddModelError("Name", "Department name already exists");
                }

                if (ModelState.IsValid)
                {
                    await _departmentService.UpdateDepartmentAsync(department);
                    TempData["SuccessMessage"] = "Department updated successfully!";
                    return RedirectToAction(nameof(Index));
                }
            }

            return View(department);
        }

        // GET: Departments/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var department = await _departmentService.GetDepartmentByIdAsync(id);
            if (department == null)
            {
                return NotFound();
            }

            return View(department);
        }

        // POST: Departments/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (!await _departmentService.CanDeleteDepartmentAsync(id))
            {
                TempData["ErrorMessage"] = "Cannot delete department. It has associated students.";
                return RedirectToAction(nameof(Index));
            }

            await _departmentService.DeleteDepartmentAsync(id);
            TempData["SuccessMessage"] = "Department deleted successfully!";
            return RedirectToAction(nameof(Index));
        }
    }
}
