import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/task.dart';
import '../services/task_service.dart';
import '../widgets/task_tile.dart';
import 'add_edit_task_screen.dart';

/// Main screen displaying the list of tasks
/// Provides a clean and intuitive interface for task management
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load tasks when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TaskService>().loadTasks();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'QuickTask',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        elevation: 0,
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.list), text: 'All'),
            Tab(icon: Icon(Icons.pending_actions), text: 'Pending'),
            Tab(icon: Icon(Icons.check_circle), text: 'Completed'),
          ],
        ),
      ),
      
      body: Consumer<TaskService>(
        builder: (context, taskService, child) {
          return Column(
            children: [
              // Search Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search tasks...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              
              // Task Statistics
              if (_searchQuery.isEmpty) _buildTaskStats(taskService),
              
              // Task Lists in Tabs
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildTaskList(taskService.searchTasks(_searchQuery), taskService),
                    _buildTaskList(
                      taskService.searchTasks(_searchQuery)
                          .where((task) => !task.isCompleted)
                          .toList(),
                      taskService,
                    ),
                    _buildTaskList(
                      taskService.searchTasks(_searchQuery)
                          .where((task) => task.isCompleted)
                          .toList(),
                      taskService,
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
      
      // Floating Action Button to add new task
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewTask,
        tooltip: 'Add New Task',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Build task statistics widget
  Widget _buildTaskStats(TaskService taskService) {
    final stats = taskService.getTaskStats();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Total', stats['total']!, Colors.blue),
          _buildStatItem('Pending', stats['pending']!, Colors.orange),
          _buildStatItem('Completed', stats['completed']!, Colors.green),
          if (stats['overdue']! > 0)
            _buildStatItem('Overdue', stats['overdue']!, Colors.red),
        ],
      ),
    );
  }

  /// Build individual stat item
  Widget _buildStatItem(String label, int count, Color color) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color.withValues(alpha: 0.8),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Build the task list widget
  Widget _buildTaskList(List<Task> tasks, TaskService taskService) {
    if (taskService.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (tasks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _searchQuery.isNotEmpty ? Icons.search_off : Icons.task_alt,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty 
                  ? 'No tasks found for "$_searchQuery"'
                  : 'No tasks yet',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            if (_searchQuery.isEmpty)
              Text(
                'Tap the + button to add your first task',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
              ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => taskService.loadTasks(),
      child: ListView.builder(
        padding: const EdgeInsets.only(bottom: 80), // Space for FAB
        itemCount: tasks.length,
        itemBuilder: (context, index) {
          final task = tasks[index];
          return TaskTile(
            task: task,
            onTap: () => _editTask(task),
            onToggle: () => taskService.toggleTaskCompletion(task),
            onDelete: () => _deleteTask(task, taskService),
          );
        },
      ),
    );
  }

  /// Navigate to add new task screen
  Future<void> _addNewTask() async {
    final Task? newTask = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddEditTaskScreen(),
      ),
    );

    if (newTask != null && mounted) {
      final success = await context.read<TaskService>().addTask(newTask);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task added successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  /// Navigate to edit task screen
  Future<void> _editTask(Task task) async {
    final Task? updatedTask = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditTaskScreen(task: task),
      ),
    );

    if (updatedTask != null && mounted) {
      final success = await context.read<TaskService>().updateTask(updatedTask);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  /// Delete a task
  Future<void> _deleteTask(Task task, TaskService taskService) async {
    final success = await taskService.deleteTask(task.id!);
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Task "${task.title}" deleted'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Undo',
            textColor: Colors.white,
            onPressed: () {
              // Re-add the task (simple undo functionality)
              taskService.addTask(task.copyWith(id: null));
            },
          ),
        ),
      );
    }
  }
}
