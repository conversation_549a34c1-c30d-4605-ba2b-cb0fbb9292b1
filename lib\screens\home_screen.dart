import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/task.dart';
import '../services/task_service.dart';
import '../widgets/task_tile.dart';
import 'add_edit_task_screen.dart';

/// Main screen displaying the list of tasks
/// Provides a clean and intuitive interface for task management
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load tasks when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TaskService>().loadTasks();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'QuickTask',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        scrolledUnderElevation: 1,
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _showSearchDialog(context),
            icon: Icon(
              Icons.search_rounded,
              color: colorScheme.onSurfaceVariant,
            ),
            tooltip: 'Search tasks',
          ),
          const SizedBox(width: 8),
        ],
      ),
      
      body: Consumer<TaskService>(
        builder: (context, taskService, child) {
          return Column(
            children: [
              // Modern Tab Bar with custom design
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: TabBar(
                  controller: _tabController,
                  indicator: BoxDecoration(
                    color: colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  dividerColor: Colors.transparent,
                  labelColor: colorScheme.onPrimary,
                  unselectedLabelColor: colorScheme.onSurfaceVariant,
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  tabs: const [
                    Tab(
                      icon: Icon(Icons.list_rounded, size: 20),
                      text: 'All',
                    ),
                    Tab(
                      icon: Icon(Icons.pending_actions_rounded, size: 20),
                      text: 'Pending',
                    ),
                    Tab(
                      icon: Icon(Icons.check_circle_rounded, size: 20),
                      text: 'Done',
                    ),
                  ],
                ),
              ),
              
              // Task Statistics
              _buildTaskStats(taskService),

              // Task Lists in Tabs
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildTaskList(taskService.tasks, taskService),
                    _buildTaskList(taskService.pendingTasks, taskService),
                    _buildTaskList(taskService.completedTasks, taskService),
                  ],
                ),
              ),
            ],
          );
        },
      ),

      // Modern Floating Action Button
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addNewTask,
        tooltip: 'Add New Task',
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        icon: const Icon(Icons.add_rounded),
        label: const Text('Add Task'),
        elevation: 6,
      ),
    );
  }

  /// Build modern task statistics widget
  Widget _buildTaskStats(TaskService taskService) {
    final stats = taskService.getTaskStats();
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Total',
              stats['total']!,
              Icons.list_rounded,
              colorScheme.primary,
              colorScheme.primaryContainer,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'Pending',
              stats['pending']!,
              Icons.pending_actions_rounded,
              colorScheme.tertiary,
              colorScheme.tertiaryContainer,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'Done',
              stats['completed']!,
              Icons.check_circle_rounded,
              colorScheme.secondary,
              colorScheme.secondaryContainer,
            ),
          ),
          if (stats['overdue']! > 0) ...[
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'Overdue',
                stats['overdue']!,
                Icons.warning_rounded,
                colorScheme.error,
                colorScheme.errorContainer,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build modern stat card
  Widget _buildStatCard(String label, int count, IconData icon, Color iconColor, Color backgroundColor) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: iconColor,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: iconColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: theme.textTheme.labelMedium?.copyWith(
              color: iconColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Build the task list widget
  Widget _buildTaskList(List<Task> tasks, TaskService taskService) {
    final colorScheme = Theme.of(context).colorScheme;

    if (taskService.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: colorScheme.primary),
            const SizedBox(height: 16),
            Text(
              'Loading tasks...',
              style: TextStyle(color: colorScheme.onSurfaceVariant),
            ),
          ],
        ),
      );
    }

    if (tasks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.task_alt_rounded,
                size: 48,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No tasks yet',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Tap "Add Task" to create your first task',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => taskService.loadTasks(),
      child: ListView.builder(
        padding: const EdgeInsets.only(bottom: 80), // Space for FAB
        itemCount: tasks.length,
        itemBuilder: (context, index) {
          final task = tasks[index];
          return TaskTile(
            task: task,
            onTap: () => _editTask(task),
            onToggle: () => taskService.toggleTaskCompletion(task),
            onDelete: () => _deleteTask(task, taskService),
          );
        },
      ),
    );
  }

  /// Navigate to add new task screen
  Future<void> _addNewTask() async {
    final Task? newTask = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddEditTaskScreen(),
      ),
    );

    if (newTask != null && mounted) {
      final success = await context.read<TaskService>().addTask(newTask);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task added successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  /// Navigate to edit task screen
  Future<void> _editTask(Task task) async {
    final Task? updatedTask = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditTaskScreen(task: task),
      ),
    );

    if (updatedTask != null && mounted) {
      final success = await context.read<TaskService>().updateTask(updatedTask);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  /// Delete a task
  Future<void> _deleteTask(Task task, TaskService taskService) async {
    final success = await taskService.deleteTask(task.id!);
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Task "${task.title}" deleted'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Undo',
            textColor: Colors.white,
            onPressed: () {
              // Re-add the task (simple undo functionality)
              taskService.addTask(task.copyWith(id: null));
            },
          ),
        ),
      );
    }
  }

  /// Show modern search dialog
  void _showSearchDialog(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.search_rounded,
          color: colorScheme.primary,
          size: 32,
        ),
        title: const Text('Search Tasks'),
        content: TextField(
          controller: _searchController,
          autofocus: true,
          decoration: InputDecoration(
            hintText: 'Enter task title or description...',
            prefixIcon: Icon(Icons.search_rounded, color: colorScheme.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchQuery = '';
              });
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }
}
