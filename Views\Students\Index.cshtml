@model StudentManagementSystem.Models.ViewModels.StudentListViewModel
@{
    ViewData["Title"] = "Students";
}

<!-- Header Section -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
    <div class="mb-4 sm:mb-0">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Students Management</h1>
        <p class="text-gray-600">Manage and view all student records</p>
    </div>
    <a href="/Students/Create" class="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors shadow-sm">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add New Student
    </a>
</div>

<!-- Filters Section -->
<div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-8">
    <form method="get" class="space-y-4 lg:space-y-0 lg:grid lg:grid-cols-12 lg:gap-4">
        <div class="lg:col-span-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Search Students
            </label>
            <input type="text" name="searchTerm" value="@Model.SearchTerm"
                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                   placeholder="Search by name or email...">
        </div>

        <div class="lg:col-span-3">
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                Department
            </label>
            <select name="departmentId" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all">
                <option value="">All Departments</option>
                @if (Model.Departments != null)
                {
                    @foreach (var dept in Model.Departments)
                    {
                        <option value="@dept.Value" selected="@dept.Selected">@dept.Text</option>
                    }
                }
            </select>
        </div>

        <div class="lg:col-span-3">
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Enrollment Year
            </label>
            <select name="enrollmentYear" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all">
                <option value="">All Years</option>
                @if (Model.EnrollmentYears != null)
                {
                    @foreach (var year in Model.EnrollmentYears)
                    {
                        <option value="@year.Value" selected="@year.Selected">@year.Text</option>
                    }
                }
            </select>
        </div>

        <div class="lg:col-span-2 lg:flex lg:items-end">
            <button type="submit" class="w-full px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"></path>
                </svg>
                Filter
            </button>
        </div>
    </form>
</div>

<!-- Results Summary -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
    <div class="text-gray-600 mb-4 sm:mb-0">
        Showing <span class="font-semibold text-gray-900">@Model.Students.Count()</span> of <span class="font-semibold text-gray-900">@Model.TotalStudents</span> students
    </div>
    <div class="flex space-x-3">
        <button class="inline-flex items-center px-4 py-2 bg-green-50 hover:bg-green-100 text-green-700 font-medium rounded-lg transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export Excel
        </button>
        <button class="inline-flex items-center px-4 py-2 bg-red-50 hover:bg-red-100 text-red-700 font-medium rounded-lg transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
            </svg>
            Export PDF
        </button>
    </div>
</div>

<!-- Students Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    @foreach (var student in Model.Students)
    {
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 card-hover">
            <!-- Student Header -->
            <div class="flex items-center space-x-4 mb-4">
                <div class="relative">
                    @if (!string.IsNullOrEmpty(student.ProfilePhotoPath))
                    {
                        <img src="~/@student.ProfilePhotoPath" alt="@student.FullName"
                             class="w-16 h-16 rounded-full object-cover">
                    }
                    else
                    {
                        <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                            @student.FullName.Substring(0, 1).ToUpper()
                        </div>
                    }
                    <div class="absolute -bottom-1 -right-1 w-6 h-6 @(student.IsActive ? "bg-green-500" : "bg-gray-400") rounded-full border-2 border-white flex items-center justify-center">
                        @if (student.IsActive)
                        {
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                            </svg>
                        }
                        else
                        {
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        }
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <h3 class="text-lg font-semibold text-gray-900 truncate">@student.FullName</h3>
                    <p class="text-sm text-gray-500">@student.StudentId</p>
                    <div class="flex items-center mt-1">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @(student.IsActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800")">
                            @(student.IsActive ? "Active" : "Inactive")
                        </span>
                    </div>
                </div>
            </div>

            <!-- Student Details -->
            <div class="space-y-3 mb-6">
                <div class="flex items-center text-sm text-gray-600">
                    <svg class="w-4 h-4 mr-3 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <span class="truncate">@student.Email</span>
                </div>

                @if (!string.IsNullOrEmpty(student.Phone))
                {
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <span>@student.Phone</span>
                    </div>
                }

                <div class="flex items-center text-sm text-gray-600">
                    <svg class="w-4 h-4 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <span>@student.Department?.Name</span>
                </div>

                <div class="flex items-center text-sm text-gray-600">
                    <svg class="w-4 h-4 mr-3 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span>Enrolled: @student.EnrollmentDate.ToString("MMM yyyy")</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-2">
                <a href="/Students/Details/@student.Id" class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-primary-50 hover:bg-primary-100 text-primary-700 font-medium rounded-lg transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View
                </a>
                <a href="/Students/Edit/@student.Id" class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-green-50 hover:bg-green-100 text-green-700 font-medium rounded-lg transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit
                </a>
                <a href="/Students/Delete/@student.Id" class="inline-flex items-center justify-center px-3 py-2 bg-red-50 hover:bg-red-100 text-red-700 font-medium rounded-lg transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </a>
            </div>
        </div>
    }
</div>

@if (!Model.Students.Any())
{
    <div class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No Students Found</h3>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">Try adjusting your search criteria or add a new student to get started.</p>
        <a href="/Students/Create" class="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add First Student
        </a>
    </div>
}

<!-- Pagination -->
@if (Model.TotalPages > 1)
{
    <nav aria-label="Students pagination" class="mt-8">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing page <span class="font-medium">@Model.CurrentPage</span> of <span class="font-medium">@Model.TotalPages</span>
            </div>
            <div class="flex items-center space-x-2">
                @if (Model.CurrentPage > 1)
                {
                    <a href="?page=@(Model.CurrentPage - 1)&searchTerm=@Model.SearchTerm&departmentId=@Model.DepartmentFilter&enrollmentYear=@Model.EnrollmentYearFilter"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 transition-colors">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Previous
                    </a>
                }

                <div class="flex items-center space-x-1">
                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                    {
                        @if (i == Model.CurrentPage)
                        {
                            <span class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-lg">
                                @i
                            </span>
                        }
                        else
                        {
                            <a href="?page=@i&searchTerm=@Model.SearchTerm&departmentId=@Model.DepartmentFilter&enrollmentYear=@Model.EnrollmentYearFilter"
                               class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 transition-colors">
                                @i
                            </a>
                        }
                    }
                </div>

                @if (Model.CurrentPage < Model.TotalPages)
                {
                    <a href="?page=@(Model.CurrentPage + 1)&searchTerm=@Model.SearchTerm&departmentId=@Model.DepartmentFilter&enrollmentYear=@Model.EnrollmentYearFilter"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 transition-colors">
                        Next
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                }
            </div>
        </div>
    </nav>
}

@section Scripts {
    <script>
        // Auto-submit form on filter change
        document.querySelectorAll('select[name="departmentId"], select[name="enrollmentYear"]').forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });

        // Live search with debounce
        let searchTimeout;
        const searchInput = document.querySelector('input[name="searchTerm"]');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.form.submit();
                }, 500);
            });
        }

        // Add loading state to buttons
        document.querySelectorAll('button[type="submit"]').forEach(button => {
            button.addEventListener('click', function() {
                this.innerHTML = `
                    <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading...
                `;
                this.disabled = true;
            });
        });
    </script>
}
