@model StudentManagementSystem.Models.ViewModels.StudentListViewModel
@{
    ViewData["Title"] = "Students";
}

<!-- Header Section -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">
            <i class="bi bi-people-fill text-primary me-2"></i>
            Students Management
        </h2>
        <p class="text-muted mb-0">Manage and view all student records</p>
    </div>
    <a href="/Students/Create" class="btn btn-gradient-primary">
        <i class="bi bi-person-plus-fill me-2"></i>
        Add New Student
    </a>
</div>

<!-- Filters Card -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label fw-semibold">
                    <i class="bi bi-search me-1"></i>
                    Search Students
                </label>
                <input type="text" name="searchTerm" value="@Model.SearchTerm" 
                       class="form-control" placeholder="Search by name or email...">
            </div>
            <div class="col-md-3">
                <label class="form-label fw-semibold">
                    <i class="bi bi-building me-1"></i>
                    Department
                </label>
                <select name="departmentId" class="form-select">
                    <option value="">All Departments</option>
                    @if (Model.Departments != null)
                    {
                        @foreach (var dept in Model.Departments)
                        {
                            <option value="@dept.Value" selected="@dept.Selected">@dept.Text</option>
                        }
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-semibold">
                    <i class="bi bi-calendar me-1"></i>
                    Enrollment Year
                </label>
                <select name="enrollmentYear" class="form-select">
                    <option value="">All Years</option>
                    @if (Model.EnrollmentYears != null)
                    {
                        @foreach (var year in Model.EnrollmentYears)
                        {
                            <option value="@year.Value" selected="@year.Selected">@year.Text</option>
                        }
                    }
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-gradient-primary w-100">
                    <i class="bi bi-funnel-fill me-1"></i>
                    Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Results Summary -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div class="text-muted">
        Showing @Model.Students.Count() of @Model.TotalStudents students
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-success btn-sm">
            <i class="bi bi-download me-1"></i>
            Export Excel
        </button>
        <button class="btn btn-outline-danger btn-sm">
            <i class="bi bi-file-pdf me-1"></i>
            Export PDF
        </button>
    </div>
</div>

<!-- Students Grid -->
<div class="row g-4">
    @foreach (var student in Model.Students)
    {
        <div class="col-lg-6 col-xl-4">
            <div class="card border-0 shadow-sm h-100 student-card">
                <div class="card-body p-4">
                    <!-- Student Header -->
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="position-relative">
                                @if (!string.IsNullOrEmpty(student.ProfilePhotoPath))
                                {
                                    <img src="~/@student.ProfilePhotoPath" alt="@student.FullName"
                                         class="rounded-circle" width="60" height="60" style="object-fit: cover;">
                                }
                                else
                                {
                                    <div class="rounded-circle bg-gradient-primary d-flex align-items-center justify-content-center text-white fw-bold"
                                         style="width: 60px; height: 60px; font-size: 1.5rem;">
                                        @student.FullName.Substring(0, 1).ToUpper()
                                    </div>
                                }
                                <span class="position-absolute bottom-0 end-0 badge rounded-pill @(student.IsActive ? "bg-success" : "bg-secondary")">
                                    <i class="bi bi-@(student.IsActive ? "check" : "x")-circle-fill"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title mb-1 fw-bold">@student.FullName</h6>
                            <p class="text-muted small mb-0">@student.StudentId</p>
                        </div>
                    </div>

                    <!-- Student Details -->
                    <div class="student-details">
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-envelope-fill text-primary me-2"></i>
                            <small class="text-muted">@student.Email</small>
                        </div>
                        @if (!string.IsNullOrEmpty(student.Phone))
                        {
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-telephone-fill text-success me-2"></i>
                                <small class="text-muted">@student.Phone</small>
                            </div>
                        }
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-building-fill text-warning me-2"></i>
                            <small class="text-muted">@student.Department?.Name</small>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-calendar-fill text-info me-2"></i>
                            <small class="text-muted">Enrolled: @student.EnrollmentDate.ToString("MMM yyyy")</small>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex gap-2">
                        <a href="/Students/Details/@student.Id" class="btn btn-outline-primary btn-sm flex-fill">
                            <i class="bi bi-eye-fill me-1"></i>
                            View
                        </a>
                        <a href="/Students/Edit/@student.Id" class="btn btn-outline-success btn-sm flex-fill">
                            <i class="bi bi-pencil-fill me-1"></i>
                            Edit
                        </a>
                        <a href="/Students/Delete/@student.Id" class="btn btn-outline-danger btn-sm">
                            <i class="bi bi-trash-fill"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@if (!Model.Students.Any())
{
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
        </div>
        <h4 class="text-muted">No Students Found</h4>
        <p class="text-muted">Try adjusting your search criteria or add a new student.</p>
        <a href="/Students/Create" class="btn btn-gradient-primary">
            <i class="bi bi-person-plus-fill me-2"></i>
            Add First Student
        </a>
    </div>
}

<!-- Pagination -->
@if (Model.TotalPages > 1)
{
    <nav aria-label="Students pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            @if (Model.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="?page=@(Model.CurrentPage - 1)&searchTerm=@Model.SearchTerm&departmentId=@Model.DepartmentFilter&enrollmentYear=@Model.EnrollmentYearFilter">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                    <a class="page-link" href="?page=@i&searchTerm=@Model.SearchTerm&departmentId=@Model.DepartmentFilter&enrollmentYear=@Model.EnrollmentYearFilter">@i</a>
                </li>
            }

            @if (Model.CurrentPage < Model.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="?page=@(Model.CurrentPage + 1)&searchTerm=@Model.SearchTerm&departmentId=@Model.DepartmentFilter&enrollmentYear=@Model.EnrollmentYearFilter">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

<style>
    .student-card {
        transition: all 0.3s ease;
        border-radius: 1rem !important;
    }

    .student-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
    }

    .student-details {
        border-left: 3px solid var(--primary-color);
        padding-left: 1rem;
        margin-left: 0.5rem;
    }

    .page-link {
        border-radius: 0.5rem !important;
        margin: 0 0.25rem;
        border: none;
        color: var(--primary-color);
    }

    .page-item.active .page-link {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border: none;
    }

    .page-link:hover {
        background-color: rgba(99, 102, 241, 0.1);
        color: var(--primary-color);
    }
</style>

@section Scripts {
    <script>
        // Auto-submit form on filter change
        document.querySelectorAll('select[name="departmentId"], select[name="enrollmentYear"]').forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });

        // Live search with debounce
        let searchTimeout;
        document.querySelector('input[name="searchTerm"]').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    </script>
}
