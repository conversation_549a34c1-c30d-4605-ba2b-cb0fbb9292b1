using StudentManagementSystem.Models;

namespace StudentManagementSystem.Data
{
    public static class SeedData
    {
        public static async Task Initialize(ApplicationDbContext context)
        {
            // Ensure departments exist
            if (!context.Departments.Any())
            {
                var departments = new List<Department>
                {
                    new Department { Name = "Computer Science", Code = "CS", Description = "Computer Science and Engineering" },
                    new Department { Name = "Business Administration", Code = "BA", Description = "Business and Management Studies" },
                    new Department { Name = "Electrical Engineering", Code = "EE", Description = "Electrical and Electronics Engineering" },
                    new Department { Name = "Mechanical Engineering", Code = "ME", Description = "Mechanical Engineering" },
                    new Department { Name = "Mathematics", Code = "MATH", Description = "Mathematics and Statistics" }
                };

                context.Departments.AddRange(departments);
                await context.SaveChangesAsync();
            }

            // Seed sample students
            if (!context.Students.Any())
            {
                var random = new Random();
                var departments = context.Departments.ToList();
                var firstNames = new[] { "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>" };
                var lastNames = new[] { "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>" };

                var students = new List<Student>();

                for (int i = 1; i <= 50; i++)
                {
                    var firstName = first<PERSON>ames[random.Next(first<PERSON>ames.Length)];
                    var lastName = lastNames[random.Next(lastNames.Length)];
                    var department = departments[random.Next(departments.Count)];
                    var enrollmentYear = random.Next(2020, 2025);
                    var birthYear = random.Next(1995, 2005);

                    var student = new Student
                    {
                        FullName = $"{firstName} {lastName}",
                        Email = $"{firstName.ToLower()}.{lastName.ToLower()}@university.edu",
                        Phone = $"+1{random.Next(100, 999)}{random.Next(100, 999)}{random.Next(1000, 9999)}",
                        DateOfBirth = new DateTime(birthYear, random.Next(1, 13), random.Next(1, 28)),
                        Gender = (Gender)random.Next(0, 3),
                        DepartmentId = department.Id,
                        EnrollmentDate = new DateTime(enrollmentYear, random.Next(1, 13), random.Next(1, 28)),
                        StudentId = $"{department.Code}{enrollmentYear}{i:D3}",
                        IsActive = random.Next(0, 10) > 1, // 90% active
                        CreatedDate = DateTime.Now.AddDays(-random.Next(0, 365)),
                        UpdatedDate = DateTime.Now
                    };

                    students.Add(student);
                }

                context.Students.AddRange(students);
                await context.SaveChangesAsync();
            }
        }
    }
}
