# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

name: "Pull Request Labeler"
on:
- pull_request_target

# Declare default permissions as read only.
permissions: read-all

jobs:
  triage:
    if: ${{ github.repository == 'flutter/flutter' }}
    permissions:
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
    # Source available at https://github.com/actions/labeler/blob/main/README.md
    - uses: actions/labeler@8558fd74291d67161a8a78ce36a881fa63b766a9
      with:
        sync-labels: true
