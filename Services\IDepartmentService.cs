using StudentManagementSystem.Models;

namespace StudentManagementSystem.Services
{
    public interface IDepartmentService
    {
        Task<IEnumerable<Department>> GetAllDepartmentsAsync();
        Task<Department?> GetDepartmentByIdAsync(int id);
        Task<Department> CreateDepartmentAsync(Department department);
        Task<Department> UpdateDepartmentAsync(Department department);
        Task<bool> DeleteDepartmentAsync(int id);
        Task<bool> DepartmentExistsAsync(int id);
        Task<bool> DepartmentCodeExistsAsync(string code, int? excludeId = null);
        Task<bool> DepartmentNameExistsAsync(string name, int? excludeId = null);
        Task<bool> CanDeleteDepartmentAsync(int id);
        Task<int> GetTotalDepartmentsAsync();
    }
}
