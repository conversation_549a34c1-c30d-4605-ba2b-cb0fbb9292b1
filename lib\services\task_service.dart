import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/task.dart';
import '../services/database_service.dart';

/// Service class for managing task operations
/// Acts as a layer between the UI and the database
class TaskService extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  List<Task> _tasks = [];
  bool _isLoading = false;

  /// Get all tasks
  List<Task> get tasks => _tasks;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get completed tasks
  List<Task> get completedTasks => _tasks.where((task) => task.isCompleted).toList();

  /// Get pending tasks
  List<Task> get pendingTasks => _tasks.where((task) => !task.isCompleted).toList();

  /// Get overdue tasks
  List<Task> get overdueTasks => _tasks.where((task) => task.isOverdue).toList();

  /// Load all tasks from the database
  Future<void> loadTasks() async {
    _isLoading = true;
    notifyListeners();

    try {
      if (kIsWeb) {
        // For web, use in-memory storage as fallback
        _tasks = _getWebFallbackTasks();
      } else {
        _tasks = await _databaseService.getAllTasks();
      }
    } catch (e) {
      debugPrint('Error loading tasks: $e');
      // Fallback to empty list on error
      _tasks = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Add a new task
  Future<bool> addTask(Task task) async {
    try {
      if (kIsWeb) {
        // For web, generate a simple ID and store in memory
        task.id = DateTime.now().millisecondsSinceEpoch;
        _tasks.insert(0, task);
      } else {
        int id = await _databaseService.insertTask(task);
        task.id = id;
        _tasks.insert(0, task); // Add to the beginning of the list
      }
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error adding task: $e');
      return false;
    }
  }

  /// Update an existing task
  Future<bool> updateTask(Task task) async {
    try {
      if (!kIsWeb) {
        await _databaseService.updateTask(task);
      }

      // Find and update the task in the local list
      int index = _tasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        _tasks[index] = task;
        notifyListeners();
      }
      return true;
    } catch (e) {
      debugPrint('Error updating task: $e');
      return false;
    }
  }

  /// Delete a task
  Future<bool> deleteTask(int taskId) async {
    try {
      if (!kIsWeb) {
        await _databaseService.deleteTask(taskId);
      }

      // Remove the task from the local list
      _tasks.removeWhere((task) => task.id == taskId);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error deleting task: $e');
      return false;
    }
  }

  /// Toggle task completion status
  Future<bool> toggleTaskCompletion(Task task) async {
    try {
      Task updatedTask = task.copyWith(
        isCompleted: !task.isCompleted,
        updatedAt: DateTime.now(),
      );

      if (!kIsWeb) {
        await _databaseService.updateTask(updatedTask);
      }

      // Update the task in the local list
      int index = _tasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        _tasks[index] = updatedTask;
        notifyListeners();
      }
      return true;
    } catch (e) {
      debugPrint('Error toggling task completion: $e');
      return false;
    }
  }

  /// Get task statistics
  Map<String, int> getTaskStats() {
    return {
      'total': _tasks.length,
      'completed': completedTasks.length,
      'pending': pendingTasks.length,
      'overdue': overdueTasks.length,
    };
  }

  /// Search tasks by title or description
  List<Task> searchTasks(String query) {
    if (query.isEmpty) return _tasks;

    query = query.toLowerCase();
    return _tasks.where((task) {
      return task.title.toLowerCase().contains(query) ||
             (task.description?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  /// Get fallback tasks for web platform (demo data)
  List<Task> _getWebFallbackTasks() {
    return [
      Task(
        id: 1,
        title: 'Welcome to QuickTask!',
        description: 'This is a demo task. On mobile, your tasks will be saved locally.',
        isCompleted: false,
        dueDate: DateTime.now().add(const Duration(days: 1)),
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      Task(
        id: 2,
        title: 'Try adding a new task',
        description: 'Click the + button to create your first task',
        isCompleted: false,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      Task(
        id: 3,
        title: 'Mark tasks as complete',
        description: 'Tap the checkbox to mark this task as done',
        isCompleted: true,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 3)),
      ),
    ];
  }
}
