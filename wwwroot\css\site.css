/* Modern Student Management System Styles */

:root {
    --primary-color: #6366F1;
    --primary-dark: #4F46E5;
    --secondary-color: #EC4899;
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --danger-color: #EF4444;
    --info-color: #06B6D4;
    --light-color: #F8FAFC;
    --dark-color: #1E293B;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Card Animations */
.card {
    transition: all 0.3s ease;
    border: none !important;
    border-radius: 1rem !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;
}

/* Button Styles */
.btn {
    border-radius: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-gradient-primary:hover {
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
    color: white;
}

.btn-gradient-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
}

.btn-gradient-success:hover {
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
    color: white;
}

.btn-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color), #D97706);
    color: white;
}

.btn-gradient-warning:hover {
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
    color: white;
}

.btn-gradient-danger {
    background: linear-gradient(135deg, var(--danger-color), #DC2626);
    color: white;
}

.btn-gradient-danger:hover {
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
    color: white;
}

.btn-gradient-info {
    background: linear-gradient(135deg, var(--info-color), #0891B2);
    color: white;
}

.btn-gradient-info:hover {
    box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
    color: white;
}

/* Form Styles */
.form-control,
.form-select {
    border-radius: 0.75rem;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

.form-control-lg,
.form-select-lg {
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
}

/* Alert Styles */
.alert {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
    color: #059669;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
    color: #DC2626;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
    color: #D97706;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(8, 145, 178, 0.1));
    color: #0891B2;
    border-left: 4px solid var(--info-color);
}

/* Badge Styles */
.badge {
    border-radius: 0.5rem;
    font-weight: 600;
}

/* Table Styles */
.table {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(99, 102, 241, 0.05);
    transform: scale(1.01);
}

/* Pagination Styles */
.pagination .page-link {
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: none;
    color: var(--primary-color);
    font-weight: 600;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
}

.pagination .page-link:hover {
    background-color: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(99, 102, 241, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color), #059669) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color), #D97706) !important;
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-color), #DC2626) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color), #0891B2) !important;
}

/* Text Colors */
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem !important;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .fs-2 {
        font-size: 1.5rem !important;
    }
    
    .fs-4 {
        font-size: 1.1rem !important;
    }
}

/* Custom Utilities */
.shadow-soft {
    box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
}

.shadow-strong {
    box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;
}

.border-radius-lg {
    border-radius: 1rem !important;
}

.border-radius-xl {
    border-radius: 1.5rem !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
