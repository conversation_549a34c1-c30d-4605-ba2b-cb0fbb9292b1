@model StudentManagementSystem.Models.ViewModels.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="row g-4">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-gradient-primary rounded-circle p-3 text-white">
                            <i class="bi bi-people-fill fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Total Students</div>
                        <div class="fs-2 fw-bold text-primary">@Model.TotalStudents</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-gradient-success rounded-circle p-3 text-white">
                            <i class="bi bi-person-check-fill fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Active Students</div>
                        <div class="fs-2 fw-bold text-success">@Model.ActiveStudents</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-gradient-warning rounded-circle p-3 text-white">
                            <i class="bi bi-building-fill fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">Departments</div>
                        <div class="fs-2 fw-bold text-warning">@Model.TotalDepartments</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-gradient-info rounded-circle p-3 text-white">
                            <i class="bi bi-person-plus-fill fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="text-muted small">New This Month</div>
                        <div class="fs-2 fw-bold text-info">@Model.NewStudentsThisMonth</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row g-4 mt-4">
    <!-- Department Statistics -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart-fill text-primary me-2"></i>
                    Students by Department
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    @foreach (var stat in Model.DepartmentStatistics)
                    {
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 rounded-3" style="background: linear-gradient(135deg, @stat.Color, @stat.Color)20;">
                                <div class="flex-shrink-0">
                                    <div class="rounded-circle p-2" style="background-color: @stat.Color; color: white;">
                                        <i class="bi bi-people-fill"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="fw-semibold">@stat.DepartmentName</div>
                                    <div class="text-muted small">@stat.StudentCount students</div>
                                </div>
                                <div class="fs-4 fw-bold" style="color: @stat.Color;">
                                    @stat.StudentCount
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning-fill text-warning me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="/Students/Create" class="btn btn-gradient-primary">
                        <i class="bi bi-person-plus-fill me-2"></i>
                        Add New Student
                    </a>
                    <a href="/Departments/Create" class="btn btn-gradient-success">
                        <i class="bi bi-building-add me-2"></i>
                        Add Department
                    </a>
                    <a href="/Students" class="btn btn-outline-primary">
                        <i class="bi bi-list-ul me-2"></i>
                        View All Students
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="bi bi-download me-2"></i>
                        Export Data
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enrollment Trends -->
<div class="row g-4 mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up text-success me-2"></i>
                    Enrollment Trends
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    @foreach (var enrollment in Model.EnrollmentStatistics)
                    {
                        var percentage = Model.EnrollmentStatistics.Any() ? (enrollment.Count * 100.0 / Model.EnrollmentStatistics.Max(e => e.Count)) : 0;
                        <div class="col-md-3">
                            <div class="text-center p-3 rounded-3 bg-light">
                                <div class="fs-3 fw-bold text-primary">@enrollment.Year</div>
                                <div class="progress mt-2" style="height: 8px;">
                                    <div class="progress-bar bg-gradient-primary" style="width: @percentage%"></div>
                                </div>
                                <div class="mt-2 text-muted">@enrollment.Count students</div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-gradient-primary {
        background: linear-gradient(135deg, #6366F1, #4F46E5) !important;
    }
    
    .bg-gradient-success {
        background: linear-gradient(135deg, #10B981, #059669) !important;
    }
    
    .bg-gradient-warning {
        background: linear-gradient(135deg, #F59E0B, #D97706) !important;
    }
    
    .bg-gradient-info {
        background: linear-gradient(135deg, #06B6D4, #0891B2) !important;
    }
    
    .progress-bar.bg-gradient-primary {
        background: linear-gradient(90deg, #6366F1, #4F46E5) !important;
    }
</style>
